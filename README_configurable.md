# Retrieval MLM - Configurable Version

This is a refactored version of the Retrieval MLM architecture that extracts all hyperparameters into a comprehensive configuration system and adds Weights & Biases (wandb) integration for experiment tracking.

## Features

- **Configuration-driven**: All hyperparameters extracted into YAML configuration files
- **Weights & Biases integration**: Comprehensive experiment tracking with metrics, hyperparameters, and model artifacts
- **DDP compatibility**: Maintains full distributed data parallel support
- **Backward compatibility**: Same model architecture and training logic as the original
- **Flexible logging**: Both tensorboard (legacy) and wandb logging support

## Files

- `retrieval_mlm_configurable.py` - Main training script with config-driven architecture
- `config.yaml` - Sample configuration file with all parameters
- `config_schema.yaml` - Configuration schema documentation
- `retrieval_mlm.py` - Original implementation (unchanged)

## Quick Start

### 1. Install Dependencies

```bash
pip install torch torchvision torchaudio
pip install numpy tiktoken pyyaml wandb tensorboard
```

### 2. Prepare Configuration

Copy and modify the sample configuration:

```bash
cp config.yaml my_experiment.yaml
# Edit my_experiment.yaml with your desired parameters
```

### 3. Single GPU Training

```bash
python retrieval_mlm_configurable.py --config my_experiment.yaml
```

### 4. Multi-GPU DDP Training

```bash
# For 8 GPUs
torchrun --standalone --nproc_per_node=8 retrieval_mlm_configurable.py --config my_experiment.yaml
```

## Configuration Structure

The configuration file is organized into several sections:

### Model Configuration
```yaml
model:
  block_size: 1024              # Maximum sequence length
  vocab_size: 50257             # Base GPT2 vocabulary size
  n_layer: 12                   # Number of transformer layers
  n_head: 12                    # Number of attention heads
  n_embd: 768                   # Embedding dimension
  time_mixing:
    d_mix_lora_attention: 28    # LoRA dimension for attention time-mixing
    d_mix_lora_mlp: 32          # LoRA dimension for MLP time-mixing
  use_compile: true             # Whether to use torch.compile
```

### Training Configuration
```yaml
training:
  total_batch_size: 1048576     # Total batch size in tokens
  micro_batch_size: 32          # Micro batch size
  sequence_length: 1024         # Sequence length
  max_lr: 2.0e-4               # Maximum learning rate
  min_lr_ratio: 0.1            # Minimum LR as ratio of max_lr
  warmup_steps: 750            # Number of warmup steps
  max_steps: 9500              # Total training steps
  weight_decay: 0.1            # Weight decay coefficient
```

### Data Configuration
```yaml
data:
  train_data_pattern: "/path/to/train_*.npy"
  val_data_pattern: "/path/to/val_*.npy"
  tokenizer_name: "gpt2"       # Tokenizer to use
  mask_prob: 0.3               # Masking probability for MLM
```

### Weights & Biases Configuration
```yaml
wandb:
  enabled: true                # Whether to use wandb logging
  project: "retrieval-mlm"     # W&B project name
  entity: null                 # W&B entity (team/username)
  name: null                   # Run name (auto-generated if null)
  group: "encoder_mlm_124M"    # Run group for organizing experiments
  tags: ["mlm", "retrieval"]  # List of tags
  log_interval: 1              # Log metrics every N steps
```

## Weights & Biases Integration

The system provides comprehensive experiment tracking:

### Automatic Logging
- **Training metrics**: loss, learning rate, gradient norm, tokens/sec
- **Validation metrics**: validation loss at regular intervals
- **Hyperparameters**: All configuration parameters automatically logged
- **System metrics**: Training time, step duration

### DDP Support
- Only the master process (rank 0) initializes wandb to avoid conflicts
- All metrics are properly synchronized across processes before logging

### Model Artifacts
- Optional model checkpoint logging to wandb (configurable)
- Code snapshot saving for reproducibility

## Advanced Configuration

### Time-mixing Architecture
The system supports configurable LoRA dimensions for the time-mixing components:

```yaml
model:
  time_mixing:
    d_mix_lora_attention: 28    # LoRA dimension for attention
    d_mix_lora_mlp: 32          # LoRA dimension for MLP
```

### DDP Settings
```yaml
advanced:
  ddp:
    find_unused_parameters: true
    backend: "nccl"
```

### Memory and Performance
```yaml
advanced:
  set_float32_matmul_precision: "high"
model:
  dtype: "bfloat16"
  use_compile: true
```

## Example Configurations

### Small Model for Testing
```yaml
# config_small.yaml
model:
  block_size: 512
  n_layer: 6
  n_head: 6
  n_embd: 384

training:
  total_batch_size: 262144      # 256K tokens
  micro_batch_size: 16
  sequence_length: 512
  max_steps: 1000

wandb:
  project: "retrieval-mlm-test"
  group: "small_model"
```

### Large Model Configuration
```yaml
# config_large.yaml
model:
  block_size: 2048
  n_layer: 24
  n_head: 16
  n_embd: 1024

training:
  total_batch_size: 2097152     # 2M tokens
  micro_batch_size: 8
  sequence_length: 2048
  max_steps: 50000

wandb:
  project: "retrieval-mlm-large"
  group: "1B_model"
```

## Migration from Original Code

To migrate from the original `retrieval_mlm.py`:

1. **Extract your current hyperparameters** from the original code
2. **Create a config file** with your parameters:
   ```bash
   cp config.yaml my_migration.yaml
   # Edit my_migration.yaml with your original parameters
   ```
3. **Update data paths** in the config file
4. **Run with the new script**:
   ```bash
   python retrieval_mlm_configurable.py --config my_migration.yaml
   ```

### Parameter Mapping

| Original Code Location | Config Section | Config Key |
|----------------------|----------------|------------|
| `EncoderConfig.n_layer` | `model` | `n_layer` |
| `EncoderConfig.n_head` | `model` | `n_head` |
| `EncoderConfig.n_embd` | `model` | `n_embd` |
| `total_batch_size = 1048576` | `training` | `total_batch_size` |
| `B = 32` | `training` | `micro_batch_size` |
| `T = 1024` | `training` | `sequence_length` |
| `max_lr = 2e-4` | `training` | `max_lr` |
| `data_pattern = "..."` | `data` | `train_data_pattern` |

## Monitoring and Logging

### Weights & Biases Dashboard

The system automatically logs:
- **Scalars**: loss curves, learning rate schedule, gradient norms
- **System**: tokens/sec, step timing, memory usage
- **Config**: All hyperparameters for easy comparison
- **Code**: Automatic code versioning for reproducibility

### Local Logging

Traditional logging is still available:
- **Log files**: Text logs in the specified log directory
- **Checkpoints**: Model checkpoints saved at regular intervals
- **TensorBoard**: Optional tensorboard logging (legacy support)

## Troubleshooting

### Common Issues

1. **Config file not found**
   ```
   FileNotFoundError: [Errno 2] No such file or directory: 'config.yaml'
   ```
   **Solution**: Ensure the config file path is correct and the file exists.

2. **YAML parsing errors**
   ```
   yaml.scanner.ScannerError: while scanning for the next token
   ```
   **Solution**: Check YAML syntax, especially indentation and special characters.

3. **Wandb authentication**
   ```
   wandb: ERROR Unable to authenticate
   ```
   **Solution**: Run `wandb login` or set `wandb.enabled: false` in config.

4. **DDP initialization errors**
   ```
   RuntimeError: Default process group has not been initialized
   ```
   **Solution**: Ensure you're using `torchrun` for multi-GPU training.

### Performance Tips

1. **Batch size tuning**: Adjust `micro_batch_size` based on GPU memory
2. **Gradient accumulation**: The system automatically calculates `grad_accum_steps`
3. **Compilation**: Set `model.use_compile: true` for better performance
4. **Mixed precision**: Uses bfloat16 by default for optimal performance

## Configuration Reference

### Complete Parameter List

See `config_schema.yaml` for the complete list of all configurable parameters with descriptions.

### Environment Variables

The system respects standard PyTorch DDP environment variables:
- `RANK`: Global rank of the process
- `LOCAL_RANK`: Local rank on the node
- `WORLD_SIZE`: Total number of processes

## Development and Customization

### Adding New Parameters

1. Add the parameter to your config YAML file
2. Access it in code using `config.get('section.parameter')`
3. Update the schema documentation in `config_schema.yaml`

### Custom Logging

To add custom metrics to wandb:
```python
log_metrics_wandb(wandb_run, {"custom_metric": value}, step, config)
```

## Comparison with Original

| Feature | Original | Configurable |
|---------|----------|-------------|
| Parameter management | Hardcoded | YAML config |
| Experiment tracking | TensorBoard only | Wandb + TensorBoard |
| Reproducibility | Manual | Automatic config logging |
| Multi-experiment | Manual code changes | Config file changes |
| DDP support | ✅ | ✅ (maintained) |
| Model architecture | ✅ | ✅ (identical) |

The refactored version maintains 100% compatibility with the original model architecture and training logic while providing a much more flexible and maintainable configuration system.