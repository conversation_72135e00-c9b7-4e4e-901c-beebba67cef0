#!/usr/bin/env python3
"""
Validation script for the refactored Retrieval MLM implementation.
This script tests all major components to ensure backward compatibility and correctness.
"""

import os
import sys
import torch
import numpy as np
import tempfile
import yaml
from pathlib import Path

# Add current directory to path
sys.path.append('.')

def test_config_system():
    """Test the configuration system"""
    print("Testing configuration system...")

    try:
        from retrieval_mlm_configurable import Config

        # Test config loading
        cfg = Config('config.yaml')
        assert cfg.get('model.n_layer') == 12
        assert cfg.get('training.max_lr') == 2.0e-4
        assert cfg.get('data.mask_prob') == 0.3

        # Test model config creation
        model_config = cfg.get_model_config()
        assert model_config.n_layer == 12
        assert model_config.n_embd == 768
        assert model_config.d_mix_lora_attention == 28

        print("✓ Configuration system tests passed")
        return True

    except Exception as e:
        print(f"✗ Configuration system test failed: {e}")
        return False

def test_model_architecture():
    """Test model architecture and forward pass"""
    print("Testing model architecture...")

    try:
        from retrieval_mlm_configurable import Config, MaskedLanguageModel

        cfg = Config('config.yaml')
        model_config = cfg.get_model_config()
        model = MaskedLanguageModel(model_config)

        # Test parameter count matches expected
        param_count = sum(p.numel() for p in model.parameters())
        print(f"  Model has {param_count:,} parameters")

        # Test forward pass
        B, T = 4, 128
        dummy_input = torch.randint(0, model_config.vocab_size, (B, T))
        dummy_labels = torch.full_like(dummy_input, -100)
        dummy_labels[:, 20:30] = dummy_input[:, 20:30]  # Mask some positions
        attention_mask = torch.ones_like(dummy_input)

        with torch.no_grad():
            logits, loss = model(dummy_input, attention_mask=attention_mask, labels=dummy_labels)

        # Validate output shapes
        assert logits.shape == (B, T, model_config.vocab_size + 4)  # +4 for special tokens
        assert loss.item() > 0  # Loss should be positive

        print("✓ Model architecture tests passed")
        return True

    except Exception as e:
        print(f"✗ Model architecture test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loader():
    """Test data loader with dummy data"""
    print("Testing data loader...")

    try:
        from retrieval_mlm_configurable import DataLoaderLite

        # Create dummy data files
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create dummy train data
            train_data = np.random.randint(0, 50257, size=10000, dtype=np.int32)
            train_file = os.path.join(temp_dir, "train_001.npy")
            np.save(train_file, train_data)

            # Create dummy val data
            val_data = np.random.randint(0, 50257, size=5000, dtype=np.int32)
            val_file = os.path.join(temp_dir, "val_001.npy")
            np.save(val_file, val_data)

            # Test data loader
            train_pattern = os.path.join(temp_dir, "train_*.npy")
            val_pattern = os.path.join(temp_dir, "val_*.npy")

            loader = DataLoaderLite(
                B=2, T=64,
                process_rank=0, num_processes=1,
                split="train",
                train_data_pattern=train_pattern,
                val_data_pattern=val_pattern,
                mask_prob=0.3
            )

            # Test batch generation
            inputs, labels, attention_mask = loader.next_batch()

            # Validate batch shapes
            assert inputs.shape == (2, 64)
            assert labels.shape == (2, 64)
            assert attention_mask.shape == (2, 64)

            # Validate masking
            masked_positions = (labels != -100).sum().item()
            assert masked_positions > 0  # Should have some masked positions

            print("✓ Data loader tests passed")
            return True

    except Exception as e:
        print(f"✗ Data loader test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_learning_rate_schedule():
    """Test learning rate schedule function"""
    print("Testing learning rate schedule...")

    try:
        from retrieval_mlm_configurable import get_lr_schedule

        max_lr = 2e-4
        min_lr_ratio = 0.1
        warmup_steps = 100
        max_steps = 1000

        # Test warmup phase
        lr_start = get_lr_schedule(0, max_lr, min_lr_ratio, warmup_steps, max_steps)
        lr_mid_warmup = get_lr_schedule(50, max_lr, min_lr_ratio, warmup_steps, max_steps)
        lr_end_warmup = get_lr_schedule(100, max_lr, min_lr_ratio, warmup_steps, max_steps)

        assert lr_start < lr_mid_warmup < lr_end_warmup
        assert abs(lr_end_warmup - max_lr) < 1e-10

        # Test decay phase
        lr_decay = get_lr_schedule(500, max_lr, min_lr_ratio, warmup_steps, max_steps)
        lr_end = get_lr_schedule(1000, max_lr, min_lr_ratio, warmup_steps, max_steps)

        assert lr_decay > lr_end
        assert abs(lr_end - max_lr * min_lr_ratio) < 1e-10

        print("✓ Learning rate schedule tests passed")
        return True

    except Exception as e:
        print(f"✗ Learning rate schedule test failed: {e}")
        return False

def test_wandb_integration():
    """Test wandb integration (without actually initializing wandb)"""
    print("Testing wandb integration...")

    try:
        from retrieval_mlm_configurable import Config, setup_wandb_logging, log_metrics_wandb

        cfg = Config('config.yaml')

        # Test with wandb disabled
        cfg.config['wandb']['enabled'] = False
        wandb_run = setup_wandb_logging(cfg, ddp_rank=0, model=None, num_params=1000000)
        assert wandb_run is None

        # Test metrics logging with None run (should not crash)
        log_metrics_wandb(None, {"test_metric": 1.0}, step=0, config=cfg)

        print("✓ Wandb integration tests passed")
        return True

    except Exception as e:
        print(f"✗ Wandb integration test failed: {e}")
        return False

def main():
    """Run all validation tests"""
    print("=" * 60)
    print("Validating Retrieval MLM Configurable Implementation")
    print("=" * 60)

    tests = [
        test_config_system,
        test_model_architecture,
        test_data_loader,
        test_learning_rate_schedule,
        test_wandb_integration,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()

    print("=" * 60)
    print(f"Validation Results: {passed}/{total} tests passed")

    if passed == total:
        print("✓ All tests passed! The refactored implementation is ready to use.")
        return 0
    else:
        print("✗ Some tests failed. Please review the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())