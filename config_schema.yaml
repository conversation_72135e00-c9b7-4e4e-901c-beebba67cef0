# Configuration Schema for Retrieval MLM
# This file serves as both documentation and default configuration

# Model Architecture Configuration
model:
  # Core architecture parameters
  block_size: 1024              # Maximum sequence length
  vocab_size: 50257             # Base GPT2 vocabulary size (special tokens added automatically)
  n_layer: 12                   # Number of transformer layers
  n_head: 12                    # Number of attention heads
  n_embd: 768                   # Embedding dimension
  n_head_4: 12                  # Additional head parameter (legacy)

  # MLM-specific parameters
  mask_prob: 0.2                # Base masking probability (used in config, actual is in data.mask_prob)

  # Time-mixing architecture parameters
  time_mixing:
    d_mix_lora_attention: 28    # LoRA dimension for attention time-mixing
    d_mix_lora_mlp: 32          # LoRA dimension for MLP time-mixing

  # Model precision and compilation
  dtype: "bfloat16"             # Model precision (bfloat16, float16, float32)
  use_compile: true             # Whether to use torch.compile for optimization

# Training Configuration
training:
  # Batch and sequence parameters
  total_batch_size: 1048576     # Total batch size in tokens (2**20, ~1M)
  micro_batch_size: 32          # Micro batch size (B)
  sequence_length: 1024         # Sequence length (T)

  # Learning rate schedule
  max_lr: 2.0e-4               # Maximum learning rate
  min_lr_ratio: 0.1            # Minimum LR as ratio of max_lr
  warmup_steps: 750            # Number of warmup steps
  max_steps: 9500              # Total training steps

  # Optimizer parameters
  base_learning_rate: 6.0e-4   # Base learning rate for optimizer setup
  weight_decay: 0.1            # Weight decay coefficient
  embedding_lr_scale: 0.1      # Learning rate scale for embedding parameters
  beta1: 0.9                   # Adam beta1
  beta2: 0.95                  # Adam beta2
  eps: 1.0e-8                  # Adam epsilon

  # Gradient and training stability
  grad_clip_norm: 1.0          # Gradient clipping norm

  # Evaluation and checkpointing
  val_eval_interval: 250       # Steps between validation evaluations
  val_loss_steps: 20           # Number of validation batches to evaluate
  checkpoint_interval: 5000    # Steps between checkpoint saves

  # Random seed
  random_seed: 1337            # Random seed for reproducibility

# Data Configuration
data:
  # Data paths (support glob patterns)
  train_data_pattern: "/home/<USER>/notebooks/modded bert/edu_fineweb10B/edufineweb_train_*.npy"
  val_data_pattern: "/home/<USER>/notebooks/modded bert/edu_fineweb10B/edufineweb_val_*.npy"

  # Tokenizer configuration
  tokenizer_name: "gpt2"       # Tokenizer to use (tiktoken encoding name)

  # MLM data processing
  mask_prob: 0.3               # Actual masking probability used in data loader

  # Special token IDs (calculated automatically based on vocab_size)
  # mask_token_id: vocab_size + 0
  # pad_token_id: vocab_size + 1
  # cls_token_id: vocab_size + 2
  # eos_token_id: vocab_size + 3

# Infrastructure Configuration
infrastructure:
  # Device and distributed training
  device_type: "cuda"          # Device type (cuda, cpu, mps)

  # Logging and checkpoints
  log_dir: "log_encoder_mlm_edufineweb_pure_lower_special_tokens_lr_batchprob"  # Base log directory
  # Note: Actual log directory will be log_dir/wandb.name/
  # Log file name will be wandb.name.txt

  # TensorBoard logging (legacy - will be replaced by wandb)
  tensorboard:
    writer_dir_path: "/home/<USER>/run_logs"
    run_group_name: "encoder_mlm_124M_edufineweb"
    run_number: "Removed"

# Weights & Biases Configuration
wandb:
  # Project configuration
  enabled: true                # Whether to use wandb logging
  project: "retrieval-mlm"     # W&B project name
  entity: null                 # W&B entity (team/username), null for default
  name: "run_001"              # Run name - used for wandb, log files, and checkpoint directories
  group: null                  # Run group for organizing experiments
  tags: []                     # List of tags for the run
  notes: ""                    # Notes for the run

  # Logging configuration
  log_interval: 1              # Log metrics every N steps
  log_gradients: false         # Whether to log gradient histograms
  log_parameters: false        # Whether to log parameter histograms
  log_model: false             # Whether to log model artifacts

  # Sync configuration for DDP
  sync_tensorboard: false      # Sync tensorboard logs to wandb

  # Additional wandb settings
  save_code: true              # Save code snapshot
  resume: "allow"              # Resume behavior (allow, must, never, auto)

# Advanced Configuration
advanced:
  # Memory and performance
  set_float32_matmul_precision: "high"  # PyTorch matmul precision

  # DDP specific settings
  ddp:
    find_unused_parameters: true        # DDP find_unused_parameters setting
    backend: "nccl"                     # DDP backend

  # Model initialization
  init:
    std: 0.02                          # Standard deviation for weight initialization
    scale_init_layers: true            # Whether to scale initialization for specific layers